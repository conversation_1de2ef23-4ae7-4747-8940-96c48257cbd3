{"name": "react-live-route", "version": "3.1.6", "description": "A living route for react-router v4", "repository": "fi3ework/react-live-route", "license": "MIT", "authors": "fi3ework", "main": "lib/index.js", "module": "lib/index.module.js", "typings": "lib/index.d.ts", "sideEffects": false, "scripts": {"build": "rm -fr lib && ts-node scripts/build.ts", "dev": "tsc -b -w --pretty", "prepublishOnly": "yarn run build", "lint": "tslint src"}, "peerDependencies": {"react": ">=16.3", "react-router-dom": ">=4", "tiny-invariant": ">=1", "tiny-warning": ">=1"}, "files": ["lib/"], "dependencies": {"babel-plugin-annotate-pure-calls": "^0.4.0", "history": "^4.9.0", "prop-types": "^15.6.1", "react-is": "^16.7.0"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.4.4", "@babel/runtime": "^7.4.5", "@types/react": "^16.7.18", "@types/react-dom": "^16.0.11", "@types/react-router-dom": "^4.3.1", "babel-plugin-dev-expression": "^0.2.1", "coveralls": "^3.0.3", "envify": "^4.1.0", "fs-extra": "^8.0.0", "react": "^16.3.2", "react-dom": "^16.3.2", "react-router": "^5.0.0", "react-router-dom": "^5.0.0", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-filesize": "^6.0.1", "rollup-plugin-node-resolve": "^4.2.4", "rollup-plugin-replace": "^2.2.0", "shelljs": "^0.8.3", "ts-node": "^8.3.0", "tsdx": "^0.5.9", "tslint": "^5.14.0", "tslint-config-alloy": "^0.2.1", "tslint-config-prettier": "^1.18.0", "typescript": "^3.3.3333"}, "keywords": ["react", "router", "route", "routing", "history", "link", "react-live-route"]}