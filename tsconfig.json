{"compilerOptions": {"baseUrl": ".", "paths": {"*": ["types/*"]}, "alwaysStrict": true, "declaration": true, "removeComments": false, "strict": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "outDir": "dist/", "module": "es2015", "target": "es5", "lib": ["es2017", "dom"], "sourceMap": true, "jsx": "react", "moduleResolution": "node", "rootDir": "src", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": false, "strictNullChecks": true, "suppressImplicitAnyIndexErrors": true, "downlevelIteration": true, "noUnusedLocals": false, "noUnusedParameters": false}, "include": ["src/**/*.tsx"], "exclude": ["node_modules", "dist", "__test__"]}