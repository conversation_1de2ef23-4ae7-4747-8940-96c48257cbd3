# Javascript Node CircleCI 2.0 configuration file
#
# Check https://circleci.com/docs/2.0/language-javascript/ for more details
#
version: 2.0
jobs:
  build:
    docker:
      # specify the version you desire here
      - image: circleci/node:8.11.4

    # Specify service dependencies here if necessary
    # CircleCI maintains a library of pre-built images
    # documented at https://circleci.com/docs/2.0/circleci-images/
    # - image: circleci/mongo:3.4.4

    working_directory: ~/repo

    steps:
      - checkout

      # Download and cache dependencies
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "package.json" }}
            # fallback to using the latest cache if no exact match is found
            - v1-dependencies-

      - run: yarn install

      - save_cache:
          key: v1-dependencies-{{ checksum "package.json" }}
          paths:
            - node_modules

      # run tests!
      # TODO: yarn lint && yarn test
      - run: yarn test

      - run:
          name: post coverage
          command: cat ./coverage/lcov.info | ./node_modules/.bin/coveralls
